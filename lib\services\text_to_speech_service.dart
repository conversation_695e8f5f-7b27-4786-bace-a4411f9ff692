import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_tts/flutter_tts.dart';
import '../core/services/service_manager.dart';
import '../core/logging/app_logger.dart';

class TextToSpeechService implements Initializable, Disposable {
  final logger = AppLogger.getLogger('TextToSpeechService');
  static final TextToSpeechService _instance = TextToSpeechService._internal();
  factory TextToSpeechService() => _instance;
  TextToSpeechService._internal();

  final FlutterTts _flutterTts = FlutterTts();
  bool _isInitialized = false;
  bool _isSpeaking = false;
  
  StreamController<bool>? _speakingController;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isSpeaking => _isSpeaking;

  // Stream
  Stream<bool> get speakingStream => _speakingController?.stream ?? const Stream.empty();

  /// Initialise le service Text-to-Speech
  @override
  Future<void> initialize() async {
    try {
      // Initialiser le contrôleur de stream
      _speakingController = StreamController<bool>.broadcast();

      // Configuration de base
      await _flutterTts.setLanguage('fr-FR'); // Français
      await _flutterTts.setSpeechRate(0.8); // Vitesse normale (0.5 = lent, 1.0 = rapide)
      await _flutterTts.setVolume(1.0); // Volume maximum
      await _flutterTts.setPitch(1.0); // Ton normal

      // Configuration des callbacks
      _flutterTts.setStartHandler(() {
        _isSpeaking = true;
        _speakingController?.add(true);
        if (kDebugMode) {
          print('TTS: Début de la synthèse vocale');
        }
      });

      _flutterTts.setCompletionHandler(() {
        _isSpeaking = false;
        _speakingController?.add(false);
        if (kDebugMode) {
          print('TTS: Fin de la synthèse vocale');
        }
      });

      _flutterTts.setErrorHandler((message) {
        _isSpeaking = false;
        _speakingController?.add(false);
        if (kDebugMode) {
          print('TTS: Erreur - $message');
        }
      });

      _flutterTts.setCancelHandler(() {
        _isSpeaking = false;
        _speakingController?.add(false);
        if (kDebugMode) {
          print('TTS: Synthèse vocale annulée');
        }
      });

      _isInitialized = true;
      logger.info('🔊 Service TTS initialisé avec succès');

    } catch (e) {
      logger.severe('🔊 Erreur initialisation TTS: $e', e);
      rethrow;
    }
  }

  /// Prononce un texte
  Future<bool> speak(String text) async {
    if (!_isInitialized) {
      if (kDebugMode) {
        print('TTS: Service non initialisé');
      }
      return false;
    }

    if (text.isEmpty) {
      if (kDebugMode) {
        print('TTS: Texte vide');
      }
      return false;
    }

    try {
      // Arrêter toute synthèse en cours
      if (_isSpeaking) {
        await stop();
        // Attendre un peu pour que l'arrêt soit effectif
        await Future.delayed(const Duration(milliseconds: 100));
      }

      if (kDebugMode) {
        print('TTS: Synthèse vocale: "$text"');
      }

      final result = await _flutterTts.speak(text);
      return result == 1; // 1 = succès
    } catch (e) {
      if (kDebugMode) {
        print('TTS: Erreur synthèse vocale: $e');
      }
      return false;
    }
  }

  /// Arrête la synthèse vocale en cours
  Future<void> stop() async {
    try {
      if (_isSpeaking) {
        await _flutterTts.stop();
        _isSpeaking = false;
        _speakingController?.add(false);
        
        if (kDebugMode) {
          print('TTS: Synthèse vocale arrêtée');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('TTS: Erreur arrêt synthèse: $e');
      }
    }
  }

  /// Met en pause la synthèse vocale
  Future<void> pause() async {
    try {
      if (_isSpeaking) {
        await _flutterTts.pause();
        
        if (kDebugMode) {
          print('TTS: Synthèse vocale en pause');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('TTS: Erreur pause synthèse: $e');
      }
    }
  }

  /// Configure la vitesse de parole
  Future<void> setSpeechRate(double rate) async {
    try {
      // rate: 0.0 (très lent) à 1.0 (très rapide)
      final clampedRate = rate.clamp(0.1, 1.0);
      await _flutterTts.setSpeechRate(clampedRate);
      
      if (kDebugMode) {
        print('TTS: Vitesse configurée: $clampedRate');
      }
    } catch (e) {
      if (kDebugMode) {
        print('TTS: Erreur configuration vitesse: $e');
      }
    }
  }

  /// Configure le volume
  Future<void> setVolume(double volume) async {
    try {
      // volume: 0.0 (silencieux) à 1.0 (maximum)
      final clampedVolume = volume.clamp(0.0, 1.0);
      await _flutterTts.setVolume(clampedVolume);
      
      if (kDebugMode) {
        print('TTS: Volume configuré: $clampedVolume');
      }
    } catch (e) {
      if (kDebugMode) {
        print('TTS: Erreur configuration volume: $e');
      }
    }
  }

  /// Configure le ton de la voix
  Future<void> setPitch(double pitch) async {
    try {
      // pitch: 0.5 (grave) à 2.0 (aigu), 1.0 = normal
      final clampedPitch = pitch.clamp(0.5, 2.0);
      await _flutterTts.setPitch(clampedPitch);
      
      if (kDebugMode) {
        print('TTS: Ton configuré: $clampedPitch');
      }
    } catch (e) {
      if (kDebugMode) {
        print('TTS: Erreur configuration ton: $e');
      }
    }
  }

  /// Obtient les langues disponibles
  Future<List<String>> getAvailableLanguages() async {
    try {
      final languages = await _flutterTts.getLanguages;
      if (languages is List) {
        return languages.cast<String>();
      }
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('TTS: Erreur récupération langues: $e');
      }
      return [];
    }
  }

  /// Configure la langue
  Future<void> setLanguage(String language) async {
    try {
      await _flutterTts.setLanguage(language);
      
      if (kDebugMode) {
        print('TTS: Langue configurée: $language');
      }
    } catch (e) {
      if (kDebugMode) {
        print('TTS: Erreur configuration langue: $e');
      }
    }
  }

  /// Messages prédéfinis pour les commandes vocales
  Future<void> speakCommandFeedback(String command) async {
    String message;
    
    switch (command) {
      case 'accueil':
        message = 'Navigation vers l\'accueil';
        break;
      case 'sos_alerte':
        message = 'Alerte SOS déclenchée';
        break;
      case 'appels':
        message = 'Ouverture des appels';
        break;
      case 'messages':
        message = 'Ouverture des messages';
        break;
      case 'position':
        message = 'Ouverture de votre position';
        break;
      case 'time':
        final now = DateTime.now();
        final timeStr = '${now.hour} heures ${now.minute}';
        message = 'Il est $timeStr';
        break;
      case 'canne':
        message = 'Ouverture de la connexion canne';
        break;
      default:
        if (command.startsWith('unknown:')) {
          final unknownCommand = command.substring(8);
          message = 'Commande non reconnue: $unknownCommand';
        } else {
          message = 'Commande exécutée';
        }
    }
    
    await speak(message);
  }

  /// Libère les ressources
  @override
  void dispose() {
    try {
      // Arrêter la synthèse de manière synchrone
      _flutterTts.stop();
      _speakingController?.close();

      _isInitialized = false;
      _isSpeaking = false;

      logger.info('🔊 Ressources TTS libérées');
    } catch (e) {
      logger.severe('🔊 Erreur libération ressources TTS: $e', e);
    }
  }
}
