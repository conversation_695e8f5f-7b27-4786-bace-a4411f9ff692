import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiService {
  // Adresse de ton backend Node.js
  final String baseUrl = 'http://192.168.252.246:3001';

  // 🔹 GET : Liste des alertes
  Future<List<dynamic>> fetchAlertes() async {
    final response = await http.get(Uri.parse('$baseUrl/alertes'));

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      print('Erreur récupération : ${response.body}');
      throw Exception('Échec du chargement des données');
    }
  }

  // 🔹 POST : Ajouter une alerte
  Future<void> addAlerte({
    required String typeAlerte,
    required String message,
    required bool status,
    required String niveauUrgence,
    required double latitude,
    required double longitude,
  }) async {
    final data = {
      "type_alerte": typeAlerte,
      "message_alerte": message,
      "status_alerte": status,
      "niveau_urgence": niveauUrgence,
      "latitude": latitude.toString(),
      "longitude": longitude.toString(),
    };

    final response = await http.post(
      Uri.parse('$baseUrl/alertes'),
      headers: {
        'Content-Type': 'application/json',
      },
      body: json.encode(data),
    );

    if (response.statusCode != 201 && response.statusCode != 200) {
      print('Erreur création : ${response.body}');
      throw Exception('Échec de la création');
    }
  }

  // 🔹 DELETE : Supprimer une alerte
  Future<void> deleteAlerte(int id) async {
    final response = await http.delete(Uri.parse('$baseUrl/alertes/$id'));

    if (response.statusCode != 204 && response.statusCode != 200) {
      print('Erreur suppression : ${response.body}');
      throw Exception('Échec de la suppression');
    }
  }
}
