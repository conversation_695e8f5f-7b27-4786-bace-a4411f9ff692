import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../core/config/app_config.dart';
import '../core/logging/app_logger.dart';
import '../core/services/service_manager.dart';
import '../core/services/connectivity_service.dart';
import '../core/services/cache_service.dart';

/// Exceptions personnalisées pour l'API
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? endpoint;

  const ApiException(this.message, [this.statusCode, this.endpoint]);

  @override
  String toString() => 'ApiException: $message (${statusCode ?? 'N/A'})';
}

class NetworkException implements Exception {
  final String message;
  const NetworkException(this.message);

  @override
  String toString() => 'NetworkException: $message';
}

/// Service API sécurisé avec gestion d'erreurs robuste
class ApiService implements Initializable, Disposable {
  final logger = AppLogger.getLogger('ApiService');
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  final String _baseUrl = AppConfig.apiBaseUrl;
  late final http.Client _client;
  late final ConnectivityService _connectivityService;
  late final CacheService _cacheService;
  bool _isInitialized = false;

  bool get isInitialized => _isInitialized;

  /// Initialise le service API
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    _client = http.Client();
    _connectivityService = ConnectivityService();
    _cacheService = CacheService();

    await _connectivityService.initialize();
    await _cacheService.initialize();

    _isInitialized = true;
    logger.info('🌐 ApiService initialisé avec baseUrl: $_baseUrl');
  }

  /// 🔹 GET : Liste des alertes avec gestion d'erreurs complète
  Future<List<dynamic>> fetchAlertes() async {
    const endpoint = '/alertes';

    try {
      final response = await _client
          .get(Uri.parse('$_baseUrl$endpoint'))
          .timeout(AppConfig.apiTimeout);

      LogUtils.logApiCall(endpoint, response.statusCode);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        logger.info('${data.length} alertes récupérées');
        return data;
      } else {
        final error = 'Erreur serveur: ${response.body}';
        LogUtils.logApiCall(endpoint, response.statusCode, error: error);
        throw ApiException(error, response.statusCode, endpoint);
      }
    } on SocketException {
      const error = 'Pas de connexion internet';
      logger.severe(error);
      throw const NetworkException(error);
    } on http.ClientException catch (e) {
      final error = 'Erreur client HTTP: $e';
      logger.severe(error);
      throw NetworkException(error);
    } catch (e) {
      final error = 'Erreur inattendue: $e';
      logger.severe(error, e);
      throw ApiException(error, null, endpoint);
    }
  }

  /// 🔹 POST : Ajouter une alerte avec gestion d'erreurs
  Future<void> addAlerte({
    required String typeAlerte,
    required String message,
    required bool status,
    required String niveauUrgence,
    required double latitude,
    required double longitude,
  }) async {
    const endpoint = '/alertes';

    final data = {
      "type_alerte": typeAlerte,
      "message_alerte": message,
      "status_alerte": status,
      "niveau_urgence": niveauUrgence,
      "latitude": latitude.toString(),
      "longitude": longitude.toString(),
    };

    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl$endpoint'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(data),
      ).timeout(AppConfig.apiTimeout);

      LogUtils.logApiCall(endpoint, response.statusCode);

      if (response.statusCode == 201 || response.statusCode == 200) {
        logger.info('Alerte créée avec succès: $typeAlerte');
        LogUtils.logSOSAlert('current_user', '$latitude,$longitude');
      } else {
        final error = 'Erreur création: ${response.body}';
        LogUtils.logApiCall(endpoint, response.statusCode, error: error);
        throw ApiException(error, response.statusCode, endpoint);
      }
    } on SocketException {
      const error = 'Pas de connexion internet';
      logger.severe(error);
      throw const NetworkException(error);
    } catch (e) {
      final error = 'Erreur création alerte: $e';
      logger.severe(error, e);
      throw ApiException(error, null, endpoint);
    }
  }

  /// 🔹 DELETE : Supprimer une alerte avec gestion d'erreurs
  Future<void> deleteAlerte(int id) async {
    final endpoint = '/alertes/$id';

    try {
      final response = await _client
          .delete(Uri.parse('$_baseUrl$endpoint'))
          .timeout(AppConfig.apiTimeout);

      LogUtils.logApiCall(endpoint, response.statusCode);

      if (response.statusCode == 204 || response.statusCode == 200) {
        logger.info('Alerte supprimée avec succès: $id');
      } else {
        final error = 'Erreur suppression: ${response.body}';
        LogUtils.logApiCall(endpoint, response.statusCode, error: error);
        throw ApiException(error, response.statusCode, endpoint);
      }
    } on SocketException {
      const error = 'Pas de connexion internet';
      logger.severe(error);
      throw const NetworkException(error);
    } catch (e) {
      final error = 'Erreur suppression alerte: $e';
      logger.severe(error, e);
      throw ApiException(error, null, endpoint);
    }
  }

  /// Libère les ressources
  @override
  void dispose() {
    _client.close();
    _connectivityService.dispose();
    _cacheService.dispose();
    _isInitialized = false;
    logger.info('🌐 ApiService fermé');
  }
}
