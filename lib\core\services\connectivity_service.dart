import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../logging/app_logger.dart';
import 'service_manager.dart';

/// Service de gestion de la connectivité réseau
class ConnectivityService implements Initializable, Disposable {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final logger = AppLogger.getLogger('ConnectivityService');
  final Connectivity _connectivity = Connectivity();
  
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  StreamController<bool>? _connectionController;
  
  bool _isConnected = false;
  bool _isInitialized = false;

  /// Stream pour écouter les changements de connectivité
  Stream<bool> get connectionStream => 
      _connectionController?.stream ?? const Stream.empty();

  /// État actuel de la connectivité
  bool get isConnected => _isConnected;
  bool get isInitialized => _isInitialized;

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      logger.info('🌐 Initialisation du service de connectivité...');
      
      _connectionController = StreamController<bool>.broadcast();
      
      // Vérifier l'état initial
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
      
      // Écouter les changements
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        _updateConnectionStatus,
        onError: (error) {
          logger.severe('🌐 Erreur surveillance connectivité: $error');
        },
      );
      
      _isInitialized = true;
      logger.info('🌐 Service de connectivité initialisé');
      
    } catch (e) {
      logger.severe('🌐 Erreur initialisation connectivité: $e', e);
      rethrow;
    }
  }

  /// Met à jour l'état de connexion
  void _updateConnectionStatus(ConnectivityResult result) {
    final wasConnected = _isConnected;
    _isConnected = result != ConnectivityResult.none;
    
    if (wasConnected != _isConnected) {
      logger.info('🌐 Connectivité changée: ${_isConnected ? "Connecté" : "Déconnecté"}');
      _connectionController?.add(_isConnected);
    }
  }

  /// Vérifie si une connexion internet est disponible
  Future<bool> hasInternetConnection() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      final result = await _connectivity.checkConnectivity();
      return result != ConnectivityResult.none;
    } catch (e) {
      logger.warning('🌐 Erreur vérification connectivité: $e');
      return false;
    }
  }

  /// Attend qu'une connexion soit disponible
  Future<void> waitForConnection({Duration? timeout}) async {
    if (_isConnected) return;
    
    final completer = Completer<void>();
    StreamSubscription<bool>? subscription;
    Timer? timeoutTimer;
    
    subscription = connectionStream.listen((connected) {
      if (connected) {
        subscription?.cancel();
        timeoutTimer?.cancel();
        if (!completer.isCompleted) {
          completer.complete();
        }
      }
    });
    
    if (timeout != null) {
      timeoutTimer = Timer(timeout, () {
        subscription?.cancel();
        if (!completer.isCompleted) {
          completer.completeError(
            TimeoutException('Timeout en attente de connexion', timeout),
          );
        }
      });
    }
    
    return completer.future;
  }

  @override
  void dispose() {
    logger.info('🌐 Fermeture du service de connectivité');
    
    _connectivitySubscription?.cancel();
    _connectionController?.close();
    
    _isInitialized = false;
    _isConnected = false;
  }
}

/// Exception pour les problèmes de connectivité
class ConnectivityException implements Exception {
  final String message;
  const ConnectivityException(this.message);
  
  @override
  String toString() => 'ConnectivityException: $message';
}
