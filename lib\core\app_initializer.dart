import 'package:flutter/foundation.dart';
import 'logging/app_logger.dart';
import 'services/service_manager.dart';
import '../services/api_service.dart';
import '../services/text_to_speech_service.dart';
import '../services/voice_command_service.dart';
import '../services/auth_service.dart';
import '../services/notification_service.dart';

/// Classe responsable de l'initialisation de l'application
class AppInitializer {
  static final AppInitializer _instance = AppInitializer._internal();
  factory AppInitializer() => _instance;
  AppInitializer._internal();

  final logger = AppLogger.getLogger('AppInitializer');
  bool _isInitialized = false;

  /// Initialise tous les services de l'application
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      logger.info('🚀 Initialisation de l\'application...');
      
      // 1. Initialiser le système de logging
      AppLogger.initialize();
      
      // 2. Enregistrer tous les services
      await _registerServices();
      
      // 3. Initialiser tous les services
      await ServiceManager().initializeAll();
      
      _isInitialized = true;
      logger.info('🚀 Application initialisée avec succès');
      
    } catch (e) {
      logger.severe('❌ Erreur lors de l\'initialisation: $e', e);
      rethrow;
    }
  }

  /// Enregistre tous les services dans le ServiceManager
  Future<void> _registerServices() async {
    final serviceManager = ServiceManager();
    
    logger.info('📋 Enregistrement des services...');
    
    // Services core
    serviceManager.register<ApiService>(ApiService());
    
    // Services de communication
    serviceManager.register<TextToSpeechService>(TextToSpeechService());
    serviceManager.register<VoiceCommandService>(VoiceCommandService());
    
    // Services d'authentification et notifications
    serviceManager.register<AuthService>(AuthService());
    serviceManager.register<NotificationService>(NotificationService());
    
    logger.info('📋 ${serviceManager.getServicesInfo()['totalServices']} services enregistrés');
  }

  /// Libère toutes les ressources
  void dispose() {
    if (!_isInitialized) return;
    
    logger.info('🔄 Fermeture de l\'application...');
    ServiceManager().dispose();
    _isInitialized = false;
    logger.info('🔄 Application fermée');
  }

  /// Vérifie si l'application est initialisée
  bool get isInitialized => _isInitialized;

  /// Obtient des informations sur l'état de l'application
  Map<String, dynamic> getAppInfo() {
    return {
      'isInitialized': _isInitialized,
      'debugMode': kDebugMode,
      'services': ServiceManager().getServicesInfo(),
    };
  }
}
