import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';
import '../logging/app_logger.dart';
import 'service_manager.dart';

/// Élément de cache avec métadonnées
class CacheItem<T> {
  final T data;
  final DateTime timestamp;
  final Duration? customExpiration;
  
  CacheItem(this.data, {this.customExpiration}) 
      : timestamp = DateTime.now();
  
  /// Vérifie si l'élément a expiré
  bool get isExpired {
    final expiration = customExpiration ?? AppConfig.cacheExpiration;
    return DateTime.now().difference(timestamp) > expiration;
  }
  
  /// Temps restant avant expiration
  Duration get timeToExpiration {
    final expiration = customExpiration ?? AppConfig.cacheExpiration;
    final elapsed = DateTime.now().difference(timestamp);
    return expiration - elapsed;
  }
}

/// Service de cache intelligent avec gestion automatique de l'expiration
class CacheService implements Initializable, Disposable {
  static final CacheService _instance = CacheService._internal();
  factory CacheService() => _instance;
  CacheService._internal();

  final logger = AppLogger.getLogger('CacheService');
  final Map<String, CacheItem> _memoryCache = {};
  SharedPreferences? _prefs;
  bool _isInitialized = false;

  static const String _cachePrefix = 'app_cache_';
  static const String _metadataPrefix = 'cache_meta_';

  bool get isInitialized => _isInitialized;

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      logger.info('💾 Initialisation du service de cache...');
      
      _prefs = await SharedPreferences.getInstance();
      
      // Nettoyer les éléments expirés au démarrage
      await _cleanExpiredItems();
      
      _isInitialized = true;
      logger.info('💾 Service de cache initialisé');
      
    } catch (e) {
      logger.severe('💾 Erreur initialisation cache: $e', e);
      rethrow;
    }
  }

  /// Stocke un élément dans le cache
  Future<void> set<T>(
    String key, 
    T data, {
    Duration? expiration,
    bool persistToDisk = true,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      final cacheItem = CacheItem<T>(data, customExpiration: expiration);
      
      // Cache en mémoire
      _memoryCache[key] = cacheItem;
      
      // Cache sur disque si demandé
      if (persistToDisk && _prefs != null) {
        final jsonData = json.encode({
          'data': _serializeData(data),
          'timestamp': cacheItem.timestamp.millisecondsSinceEpoch,
          'expiration': expiration?.inMilliseconds,
        });
        
        await _prefs!.setString('$_cachePrefix$key', jsonData);
      }
      
      logger.info('💾 Élément mis en cache: $key');
      
      // Nettoyer si le cache devient trop grand
      if (_memoryCache.length > AppConfig.maxCacheSize) {
        await _cleanOldestItems();
      }
      
    } catch (e) {
      logger.warning('💾 Erreur mise en cache de $key: $e');
    }
  }

  /// Récupère un élément du cache
  Future<T?> get<T>(String key, {T Function(dynamic)? deserializer}) async {
    if (!_isInitialized) await initialize();

    try {
      // Vérifier d'abord le cache mémoire
      final memoryItem = _memoryCache[key];
      if (memoryItem != null) {
        if (!memoryItem.isExpired) {
          logger.info('💾 Cache hit (mémoire): $key');
          return memoryItem.data as T;
        } else {
          _memoryCache.remove(key);
        }
      }
      
      // Vérifier le cache disque
      if (_prefs != null) {
        final jsonData = _prefs!.getString('$_cachePrefix$key');
        if (jsonData != null) {
          final cacheData = json.decode(jsonData);
          final timestamp = DateTime.fromMillisecondsSinceEpoch(cacheData['timestamp']);
          final expirationMs = cacheData['expiration'] as int?;
          final expiration = expirationMs != null 
              ? Duration(milliseconds: expirationMs)
              : AppConfig.cacheExpiration;
          
          if (DateTime.now().difference(timestamp) <= expiration) {
            final data = deserializer != null 
                ? deserializer(cacheData['data'])
                : cacheData['data'] as T;
            
            // Remettre en cache mémoire
            _memoryCache[key] = CacheItem<T>(data, customExpiration: expiration);
            
            logger.info('💾 Cache hit (disque): $key');
            return data;
          } else {
            // Supprimer l'élément expiré
            await _prefs!.remove('$_cachePrefix$key');
          }
        }
      }
      
      logger.info('💾 Cache miss: $key');
      return null;
      
    } catch (e) {
      logger.warning('💾 Erreur récupération cache $key: $e');
      return null;
    }
  }

  /// Supprime un élément du cache
  Future<void> remove(String key) async {
    if (!_isInitialized) await initialize();

    _memoryCache.remove(key);
    await _prefs?.remove('$_cachePrefix$key');
    
    logger.info('💾 Élément supprimé du cache: $key');
  }

  /// Vide tout le cache
  Future<void> clear() async {
    if (!_isInitialized) await initialize();

    _memoryCache.clear();
    
    if (_prefs != null) {
      final keys = _prefs!.getKeys()
          .where((key) => key.startsWith(_cachePrefix))
          .toList();
      
      for (final key in keys) {
        await _prefs!.remove(key);
      }
    }
    
    logger.info('💾 Cache vidé complètement');
  }

  /// Nettoie les éléments expirés
  Future<void> _cleanExpiredItems() async {
    // Nettoyer le cache mémoire
    final expiredKeys = _memoryCache.entries
        .where((entry) => entry.value.isExpired)
        .map((entry) => entry.key)
        .toList();
    
    for (final key in expiredKeys) {
      _memoryCache.remove(key);
    }
    
    // Nettoyer le cache disque
    if (_prefs != null) {
      final cacheKeys = _prefs!.getKeys()
          .where((key) => key.startsWith(_cachePrefix))
          .toList();
      
      for (final key in cacheKeys) {
        try {
          final jsonData = _prefs!.getString(key);
          if (jsonData != null) {
            final cacheData = json.decode(jsonData);
            final timestamp = DateTime.fromMillisecondsSinceEpoch(cacheData['timestamp']);
            final expirationMs = cacheData['expiration'] as int?;
            final expiration = expirationMs != null 
                ? Duration(milliseconds: expirationMs)
                : AppConfig.cacheExpiration;
            
            if (DateTime.now().difference(timestamp) > expiration) {
              await _prefs!.remove(key);
            }
          }
        } catch (e) {
          // Supprimer les éléments corrompus
          await _prefs!.remove(key);
        }
      }
    }
    
    if (expiredKeys.isNotEmpty) {
      logger.info('💾 ${expiredKeys.length} éléments expirés nettoyés');
    }
  }

  /// Nettoie les éléments les plus anciens
  Future<void> _cleanOldestItems() async {
    final sortedEntries = _memoryCache.entries.toList()
      ..sort((a, b) => a.value.timestamp.compareTo(b.value.timestamp));
    
    final itemsToRemove = sortedEntries.length - AppConfig.maxCacheSize + 10;
    
    for (int i = 0; i < itemsToRemove && i < sortedEntries.length; i++) {
      final key = sortedEntries[i].key;
      _memoryCache.remove(key);
      await _prefs?.remove('$_cachePrefix$key');
    }
    
    logger.info('💾 $itemsToRemove anciens éléments supprimés');
  }

  /// Sérialise les données pour le stockage
  dynamic _serializeData<T>(T data) {
    if (data is String || data is num || data is bool || data == null) {
      return data;
    } else if (data is List || data is Map) {
      return data;
    } else {
      // Pour les objets complexes, essayer de les convertir en JSON
      try {
        return (data as dynamic).toJson();
      } catch (e) {
        return data.toString();
      }
    }
  }

  /// Obtient des statistiques du cache
  Map<String, dynamic> getStats() {
    return {
      'memoryItems': _memoryCache.length,
      'maxSize': AppConfig.maxCacheSize,
      'expiredItems': _memoryCache.values.where((item) => item.isExpired).length,
    };
  }

  @override
  void dispose() {
    logger.info('💾 Fermeture du service de cache');
    _memoryCache.clear();
    _isInitialized = false;
  }
}
